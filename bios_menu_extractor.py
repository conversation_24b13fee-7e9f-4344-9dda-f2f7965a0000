#!/usr/bin/env python3
"""
BIOS菜单结构提取器
基于SOL (Serial Over LAN) 连接，通过模拟按键操作来获取完整的BIOS菜单结构信息
"""

import subprocess
import time
import os
import fcntl
import select
import re
import json
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict

@dataclass
class BiosMenuItem:
    """BIOS菜单项数据结构"""
    name: str
    position: Tuple[int, int]  # (row, col)
    is_selected: bool = False
    is_submenu: bool = False
    value: Optional[str] = None
    options: List[str] = None
    
@dataclass 
class BiosMenu:
    """BIOS菜单页面数据结构"""
    title: str
    items: List[BiosMenuItem]
    current_selection: int = 0
    raw_content: str = ""

@dataclass
class BiosMenuStructure:
    """完整的BIOS菜单结构"""
    main_menus: List[BiosMenu]
    current_menu_index: int = 0
    navigation_path: List[str] = None

class BiosNavigator:
    """BIOS导航和菜单结构提取器"""
    
    # ANSI转义序列定义
    KEY_UP = '\x1b[A'
    KEY_DOWN = '\x1b[B' 
    KEY_RIGHT = '\x1b[C'
    KEY_LEFT = '\x1b[D'
    KEY_ENTER = '\r'
    KEY_ESC = '\x1b'
    KEY_F10 = '\x1b[21~'  # 通常用于保存并退出
    
    def __init__(self, host: str, username: str, password: str):
        self.host = host
        self.username = username
        self.password = password
        self.sol_process = None
        self.menu_structure = BiosMenuStructure(main_menus=[], navigation_path=[])
        
    def connect_sol(self) -> bool:
        """建立SOL连接"""
        cmd = ['ipmitool', '-I', 'lanplus', '-H', self.host,
               '-U', self.username, '-P', self.password,
               'sol', 'activate']
        
        print(f"连接到 {self.host}...")
        try:
            self.sol_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            time.sleep(3)  # 等待连接建立
            
            if self.sol_process.poll() is not None:
                _, stderr = self.sol_process.communicate()
                print(f"SOL连接失败: {stderr}")
                return False
                
            # 设置非阻塞I/O
            self._setup_nonblocking_io()
            print("SOL连接成功")
            return True
            
        except Exception as e:
            print(f"SOL连接异常: {e}")
            return False
    
    def _setup_nonblocking_io(self):
        """设置非阻塞I/O"""
        if self.sol_process:
            fd_out = self.sol_process.stdout.fileno()
            fd_err = self.sol_process.stderr.fileno()
            
            fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
            fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)
            
            fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
            fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)
    
    def send_key(self, key: str, wait_time: float = 0.5) -> bool:
        """发送按键"""
        if not self.sol_process or self.sol_process.poll() is not None:
            print("SOL连接已断开")
            return False
            
        try:
            self.sol_process.stdin.write(key)
            self.sol_process.stdin.flush()
            time.sleep(wait_time)
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def read_screen_content(self, timeout: float = 5.0) -> str:
        """读取屏幕内容"""
        if not self.sol_process:
            return ""
            
        outputs = []
        start_time = time.time()
        no_data_count = 0
        max_no_data = int(timeout * 10)  # 0.1秒间隔
        
        while time.time() - start_time < timeout:
            if self.sol_process.poll() is not None:
                break
                
            ready, _, _ = select.select([self.sol_process.stdout], [], [], 0.1)
            if not ready:
                no_data_count += 1
                if no_data_count > max_no_data:
                    break
                continue
                
            no_data_count = 0
            try:
                fd = self.sol_process.stdout.fileno()
                data = os.read(fd, 4096)
                if data:
                    outputs.append(data.decode('utf-8', errors='ignore'))
            except Exception as e:
                print(f"读取屏幕内容出错: {e}")
                break
                
        return ''.join(outputs)
    
    def parse_bios_menu(self, content: str) -> BiosMenu:
        """解析BIOS菜单内容"""
        lines = content.split('\n')
        menu_items = []
        title = ""
        current_selection = 0
        
        # 查找菜单标题（通常在顶部）
        for i, line in enumerate(lines[:10]):
            line = line.strip()
            if line and not line.startswith('[') and len(line) > 5:
                if any(keyword in line.lower() for keyword in ['setup', 'bios', 'configuration', 'menu']):
                    title = line
                    break
        
        # 解析菜单项
        for i, line in enumerate(lines):
            line_clean = line.strip()
            if not line_clean:
                continue
                
            # 检测菜单项模式
            is_selected = False
            is_submenu = False
            item_name = ""
            value = None
            
            # 检测选中状态
            if '>' in line or line_clean.startswith('[') or line_clean.startswith('*'):
                is_selected = True
                current_selection = len(menu_items)
            
            # 提取菜单项名称
            clean_line = re.sub(r'^[\s\>\[\]\*\-\+]*', '', line_clean)
            
            # 检测是否有值（如 "Boot Order    : UEFI"）
            if ':' in clean_line:
                parts = clean_line.split(':', 1)
                item_name = parts[0].strip()
                value = parts[1].strip() if len(parts) > 1 else None
            else:
                item_name = clean_line
            
            # 检测子菜单
            if any(keyword in item_name.lower() for keyword in ['setup', 'configuration', 'advanced', 'security']):
                is_submenu = True
            
            # 过滤有效的菜单项
            if item_name and len(item_name) > 1 and not item_name.isspace():
                position = (i, line.find(item_name) if item_name in line else 0)
                
                menu_item = BiosMenuItem(
                    name=item_name,
                    position=position,
                    is_selected=is_selected,
                    is_submenu=is_submenu,
                    value=value
                )
                menu_items.append(menu_item)
        
        return BiosMenu(
            title=title or "BIOS Menu",
            items=menu_items,
            current_selection=current_selection,
            raw_content=content
        )

    def navigate_to_main_menu(self) -> bool:
        """导航到主菜单"""
        print("导航到BIOS主菜单...")

        # 多次按ESC键确保回到主菜单
        for _ in range(5):
            if not self.send_key(self.KEY_ESC, 1.0):
                return False

        # 读取当前屏幕内容
        content = self.read_screen_content()
        if content:
            print("成功到达主菜单")
            return True

        return False

    def scan_main_menu_tabs(self) -> List[BiosMenu]:
        """扫描主菜单的所有标签页"""
        main_menus = []

        # 确保在主菜单
        if not self.navigate_to_main_menu():
            return main_menus

        print("开始扫描主菜单标签页...")

        # 先移动到最左边的标签
        for _ in range(10):
            self.send_key(self.KEY_LEFT, 0.3)

        # 遍历所有标签页
        visited_titles = set()
        max_tabs = 10  # 最多扫描10个标签页

        for tab_index in range(max_tabs):
            print(f"扫描第 {tab_index + 1} 个标签页...")

            # 读取当前标签页内容
            content = self.read_screen_content(3.0)
            if not content:
                print("无法读取标签页内容")
                break

            # 解析菜单
            menu = self.parse_bios_menu(content)

            # 检查是否是新的标签页
            if menu.title not in visited_titles and menu.items:
                visited_titles.add(menu.title)
                main_menus.append(menu)
                print(f"发现标签页: {menu.title} (包含 {len(menu.items)} 个菜单项)")
            else:
                print("到达重复或空标签页，停止扫描")
                break

            # 移动到下一个标签页
            if tab_index < max_tabs - 1:
                self.send_key(self.KEY_RIGHT, 0.5)

        return main_menus

    def explore_submenu(self, menu_item: BiosMenuItem, depth: int = 0) -> Optional[BiosMenu]:
        """探索子菜单"""
        if depth > 3:  # 限制递归深度
            return None

        print(f"{'  ' * depth}探索子菜单: {menu_item.name}")

        # 进入子菜单
        if not self.send_key(self.KEY_ENTER, 1.0):
            return None

        # 读取子菜单内容
        content = self.read_screen_content(3.0)
        if not content:
            return None

        # 解析子菜单
        submenu = self.parse_bios_menu(content)
        print(f"{'  ' * depth}子菜单 '{submenu.title}' 包含 {len(submenu.items)} 个项目")

        # 返回上级菜单
        self.send_key(self.KEY_ESC, 1.0)

        return submenu

    def extract_complete_menu_structure(self) -> BiosMenuStructure:
        """提取完整的BIOS菜单结构"""
        print("=== 开始提取BIOS菜单结构 ===")

        if not self.connect_sol():
            print("SOL连接失败")
            return self.menu_structure

        try:
            # 扫描主菜单标签页
            main_menus = self.scan_main_menu_tabs()

            if not main_menus:
                print("未发现任何主菜单")
                return self.menu_structure

            # 为每个主菜单探索子菜单
            for menu_index, main_menu in enumerate(main_menus):
                print(f"\n处理主菜单 {menu_index + 1}: {main_menu.title}")

                # 导航到对应的标签页
                self.navigate_to_main_menu()
                for _ in range(10):  # 先到最左边
                    self.send_key(self.KEY_LEFT, 0.2)
                for _ in range(menu_index):  # 然后移动到目标标签
                    self.send_key(self.KEY_RIGHT, 0.3)

                # 探索当前标签页的子菜单
                for item in main_menu.items:
                    if item.is_submenu:
                        # 导航到该菜单项
                        self.navigate_to_menu_item(item, main_menu)

                        # 探索子菜单
                        submenu = self.explore_submenu(item)
                        if submenu:
                            # 可以在这里进一步处理子菜单信息
                            pass

            self.menu_structure.main_menus = main_menus
            print(f"\n=== 菜单结构提取完成，共发现 {len(main_menus)} 个主菜单 ===")

        except Exception as e:
            print(f"提取菜单结构时出错: {e}")
        finally:
            self.disconnect()

        return self.menu_structure

    def navigate_to_menu_item(self, target_item: BiosMenuItem, menu: BiosMenu):
        """导航到指定的菜单项"""
        current_pos = menu.current_selection
        target_pos = -1

        # 找到目标项的位置
        for i, item in enumerate(menu.items):
            if item.name == target_item.name:
                target_pos = i
                break

        if target_pos == -1:
            return False

        # 计算需要移动的步数
        steps = target_pos - current_pos

        if steps > 0:
            # 向下移动
            for _ in range(steps):
                self.send_key(self.KEY_DOWN, 0.3)
        elif steps < 0:
            # 向上移动
            for _ in range(abs(steps)):
                self.send_key(self.KEY_UP, 0.3)

        return True

    def disconnect(self):
        """断开SOL连接"""
        if self.sol_process:
            try:
                # 发送退出命令
                self.send_key('~.', 0.5)  # ipmitool sol的退出序列
                self.sol_process.terminate()
                self.sol_process.wait(timeout=5)
            except Exception as e:
                print(f"断开连接时出错: {e}")
                self.sol_process.kill()
            finally:
                self.sol_process = None
        print("SOL连接已断开")

    def save_menu_structure(self, filename: str = "bios_menu_structure.json"):
        """保存菜单结构到JSON文件"""
        try:
            # 转换为可序列化的字典
            data = {
                'main_menus': [asdict(menu) for menu in self.menu_structure.main_menus],
                'current_menu_index': self.menu_structure.current_menu_index,
                'navigation_path': self.menu_structure.navigation_path or [],
                'extraction_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'host': self.host
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"菜单结构已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存菜单结构失败: {e}")
            return False

    def print_menu_structure(self):
        """打印菜单结构概览"""
        print("\n=== BIOS菜单结构概览 ===")

        for i, menu in enumerate(self.menu_structure.main_menus):
            print(f"\n主菜单 {i+1}: {menu.title}")
            print(f"  包含 {len(menu.items)} 个菜单项:")

            for j, item in enumerate(menu.items):
                status = ""
                if item.is_selected:
                    status += " [当前选中]"
                if item.is_submenu:
                    status += " [子菜单]"
                if item.value:
                    status += f" (值: {item.value})"

                print(f"    {j+1}. {item.name}{status}")


def main():
    """主程序入口"""
    # 配置连接参数（从sol.py中获取的参数）
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"

    print("BIOS菜单结构提取器")
    print(f"目标主机: {HOST}")

    # 创建导航器实例
    navigator = BiosNavigator(HOST, USERNAME, PASSWORD)

    try:
        # 提取菜单结构
        menu_structure = navigator.extract_complete_menu_structure()

        if menu_structure.main_menus:
            # 打印结构概览
            navigator.print_menu_structure()

            # 保存到文件
            navigator.save_menu_structure()

            print(f"\n成功提取了 {len(menu_structure.main_menus)} 个主菜单的结构信息")
        else:
            print("未能提取到菜单结构信息")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        navigator.disconnect()


if __name__ == "__main__":
    main()
