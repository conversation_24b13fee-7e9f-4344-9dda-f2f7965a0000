#!/usr/bin/env python3
"""
BIOS菜单提取器测试脚本
用于测试和验证BIOS菜单结构提取功能
"""

from bios_menu_extractor import BiosNavigator, BiosMenuItem, BiosMenu
import time

def test_menu_parsing():
    """测试菜单解析功能"""
    print("=== 测试菜单解析功能 ===")
    
    # 模拟BIOS菜单内容
    sample_bios_content = """
    Phoenix BIOS Setup Utility
    
    Main    Advanced    Security    Boot    Exit
    
    > System Information
      System Time         : 14:30:25
      System Date         : 07/10/2025
      BIOS Version        : 1.23.45
      CPU Type            : Intel Core i7
      Total Memory        : 16384 MB
      
      Advanced Configuration
      Security Settings
      Boot Options
    """
    
    navigator = BiosNavigator("test", "test", "test")
    menu = navigator.parse_bios_menu(sample_bios_content)
    
    print(f"解析结果:")
    print(f"  标题: {menu.title}")
    print(f"  菜单项数量: {len(menu.items)}")
    print(f"  当前选中: {menu.current_selection}")
    
    for i, item in enumerate(menu.items):
        print(f"  {i+1}. {item.name}")
        if item.value:
            print(f"     值: {item.value}")
        if item.is_selected:
            print(f"     [选中]")
        if item.is_submenu:
            print(f"     [子菜单]")

def test_connection_only():
    """仅测试SOL连接（不进行菜单操作）"""
    print("=== 测试SOL连接 ===")
    
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"
    
    navigator = BiosNavigator(HOST, USERNAME, PASSWORD)
    
    if navigator.connect_sol():
        print("SOL连接成功")
        
        # 读取当前屏幕内容
        print("读取当前屏幕内容...")
        content = navigator.read_screen_content(5.0)
        
        if content:
            print(f"读取到 {len(content)} 字符的内容")
            print("前200个字符:")
            print(repr(content[:200]))
            
            # 解析当前屏幕
            menu = navigator.parse_bios_menu(content)
            print(f"\n解析结果: {menu.title}, {len(menu.items)} 个菜单项")
        else:
            print("未读取到屏幕内容")
        
        navigator.disconnect()
    else:
        print("SOL连接失败")

def test_single_navigation():
    """测试单次导航操作"""
    print("=== 测试单次导航操作 ===")
    
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"
    
    navigator = BiosNavigator(HOST, USERNAME, PASSWORD)
    
    if navigator.connect_sol():
        print("连接成功，开始导航测试...")
        
        # 读取初始状态
        print("1. 读取初始屏幕...")
        initial_content = navigator.read_screen_content(3.0)
        if initial_content:
            initial_menu = navigator.parse_bios_menu(initial_content)
            print(f"   初始菜单: {initial_menu.title}")
        
        # 发送右箭头键
        print("2. 发送右箭头键...")
        navigator.send_key(navigator.KEY_RIGHT, 1.0)
        
        # 读取变化后的内容
        print("3. 读取变化后的屏幕...")
        new_content = navigator.read_screen_content(3.0)
        if new_content:
            new_menu = navigator.parse_bios_menu(new_content)
            print(f"   新菜单: {new_menu.title}")
            
            # 比较变化
            if initial_content != new_content:
                print("   屏幕内容已发生变化")
            else:
                print("   屏幕内容未发生变化")
        
        navigator.disconnect()
    else:
        print("SOL连接失败")

def interactive_test():
    """交互式测试"""
    print("=== 交互式测试 ===")
    print("可用命令:")
    print("  1 - 测试菜单解析")
    print("  2 - 测试SOL连接")
    print("  3 - 测试单次导航")
    print("  4 - 完整菜单提取")
    print("  q - 退出")
    
    while True:
        choice = input("\n请选择测试项目 (1-4, q): ").strip()
        
        if choice == '1':
            test_menu_parsing()
        elif choice == '2':
            test_connection_only()
        elif choice == '3':
            test_single_navigation()
        elif choice == '4':
            print("开始完整菜单提取...")
            from bios_menu_extractor import main
            main()
        elif choice.lower() == 'q':
            break
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    print("BIOS菜单提取器测试工具")
    print("=" * 40)
    
    try:
        interactive_test()
    except KeyboardInterrupt:
        print("\n测试中断")
    except Exception as e:
        print(f"测试出错: {e}")
    
    print("测试结束")
