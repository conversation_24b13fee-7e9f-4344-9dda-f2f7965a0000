#!/usr/bin/env python3
"""
获取BIOS Main菜单信息的专用脚本
基于sol.py的SOL连接机制，专门获取Main菜单下的内容
"""

import subprocess
import time
import os
import fcntl
import select
import json
from datetime import datetime

class MainMenuExtractor:
    """Main菜单信息提取器"""
    
    def __init__(self, host="************", username="Administrator", password="Superuser9!"):
        self.host = host
        self.username = username
        self.password = password
        self.sol_process = None
    
    def connect_sol(self):
        """建立SOL连接"""
        cmd = ['ipmitool', '-I', 'lanplus', '-H', self.host,
               '-U', self.username, '-P', self.password,
               'sol', 'activate']
        
        print(f"连接到 {self.host}...")
        try:
            self.sol_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            time.sleep(3)  # 等待连接建立
            
            if self.sol_process.poll() is not None:
                _, stderr = self.sol_process.communicate()
                print(f"SOL连接失败: {stderr}")
                return False
            
            # 设置非阻塞I/O
            self._setup_nonblocking_io()
            print("SOL连接成功")
            return True
            
        except Exception as e:
            print(f"SOL连接异常: {e}")
            return False
    
    def _setup_nonblocking_io(self):
        """设置非阻塞I/O"""
        if self.sol_process:
            fd_out = self.sol_process.stdout.fileno()
            fd_err = self.sol_process.stderr.fileno()
            
            fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
            fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)
            
            fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
            fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)
    
    def send_key(self, key, wait_time=0.5):
        """发送按键"""
        if not self.sol_process or self.sol_process.poll() is not None:
            print("SOL连接已断开")
            return False
        
        try:
            self.sol_process.stdin.write(key)
            self.sol_process.stdin.flush()
            time.sleep(wait_time)
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def read_screen_content(self, timeout=5.0):
        """读取屏幕内容"""
        if not self.sol_process:
            return ""
        
        outputs = []
        start_time = time.time()
        no_data_count = 0
        max_no_data = int(timeout * 10)
        
        while time.time() - start_time < timeout:
            if self.sol_process.poll() is not None:
                break
            
            ready, _, _ = select.select([self.sol_process.stdout], [], [], 0.1)
            if not ready:
                no_data_count += 1
                if no_data_count > max_no_data:
                    break
                continue
            
            no_data_count = 0
            try:
                fd = self.sol_process.stdout.fileno()
                data = os.read(fd, 4096)
                if data:
                    outputs.append(data.decode('utf-8', errors='ignore'))
            except Exception as e:
                print(f"读取屏幕内容出错: {e}")
                break
        
        return ''.join(outputs)
    
    def navigate_to_main_menu(self):
        """导航到Main菜单"""
        print("导航到Main菜单...")
        
        # 多次按ESC确保回到主菜单
        for _ in range(5):
            self.send_key('\x1b', 1.0)  # ESC键
        
        # 移动到最左边的标签（通常是Main）
        for _ in range(10):
            self.send_key('\x1b[D', 0.3)  # 左箭头
        
        print("已导航到Main菜单")
        return True
    
    def get_main_menu_content(self):
        """获取Main菜单的完整内容"""
        print("=== 开始获取Main菜单内容 ===")
        
        if not self.connect_sol():
            return None
        
        try:
            # 导航到Main菜单
            self.navigate_to_main_menu()
            
            # 读取初始内容
            print("读取Main菜单初始内容...")
            initial_content = self.read_screen_content(3.0)
            
            if not initial_content:
                print("无法读取Main菜单内容")
                return None
            
            print(f"读取到 {len(initial_content)} 字符的内容")
            
            # 尝试通过上下箭头遍历菜单项
            print("通过导航键遍历菜单项...")
            menu_contents = [initial_content]
            
            # 先移动到菜单顶部
            for _ in range(20):
                self.send_key('\x1b[A', 0.2)  # 上箭头
            
            # 然后逐项向下移动，收集每个状态的屏幕内容
            for i in range(15):  # 最多尝试15个菜单项
                print(f"移动到菜单项 {i+1}...")
                self.send_key('\x1b[B', 0.5)  # 下箭头
                
                content = self.read_screen_content(2.0)
                if content and content not in menu_contents:
                    menu_contents.append(content)
                    print(f"  收集到新的菜单状态 ({len(content)} 字符)")
                else:
                    print(f"  内容重复或为空，可能已遍历完成")
                    if i > 5:  # 至少尝试几次
                        break
            
            return {
                'timestamp': datetime.now().isoformat(),
                'host': self.host,
                'menu_type': 'Main',
                'total_states': len(menu_contents),
                'contents': menu_contents
            }
            
        except Exception as e:
            print(f"获取Main菜单内容时出错: {e}")
            return None
        finally:
            self.disconnect()
    
    def parse_and_display_menu(self, menu_data):
        """解析并显示菜单内容"""
        if not menu_data:
            print("没有菜单数据可显示")
            return
        
        print(f"\n=== Main菜单解析结果 ===")
        print(f"获取时间: {menu_data['timestamp']}")
        print(f"主机: {menu_data['host']}")
        print(f"收集到 {menu_data['total_states']} 个不同的菜单状态")
        
        # 分析第一个内容（通常是最完整的）
        if menu_data['contents']:
            main_content = menu_data['contents'][0]
            print(f"\n主要内容分析 (前1000字符):")
            print("-" * 50)
            print(main_content[:1000])
            print("-" * 50)
            
            # 简单解析菜单项
            lines = main_content.split('\n')
            menu_items = []
            
            for line in lines:
                line = line.strip()
                if line and len(line) > 3:
                    # 查找可能的菜单项
                    if any(indicator in line for indicator in ['>', '*', ':', '[', ']']):
                        menu_items.append(line)
            
            if menu_items:
                print(f"\n识别到的菜单项 ({len(menu_items)} 个):")
                for i, item in enumerate(menu_items[:20]):  # 显示前20个
                    print(f"  {i+1:2d}. {item}")
                
                if len(menu_items) > 20:
                    print(f"  ... 还有 {len(menu_items) - 20} 个菜单项")
    
    def save_menu_data(self, menu_data, filename="main_menu_data.json"):
        """保存菜单数据到文件"""
        if not menu_data:
            print("没有数据可保存")
            return False
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(menu_data, f, indent=2, ensure_ascii=False)
            print(f"菜单数据已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def disconnect(self):
        """断开SOL连接"""
        if self.sol_process:
            try:
                self.send_key('~.', 0.5)  # ipmitool sol退出序列
                self.sol_process.terminate()
                self.sol_process.wait(timeout=5)
            except Exception as e:
                print(f"断开连接时出错: {e}")
                self.sol_process.kill()
            finally:
                self.sol_process = None
        print("SOL连接已断开")


def main():
    """主函数"""
    print("BIOS Main菜单信息提取器")
    print("=" * 40)
    
    extractor = MainMenuExtractor()
    
    try:
        # 获取Main菜单内容
        menu_data = extractor.get_main_menu_content()
        
        if menu_data:
            # 解析并显示
            extractor.parse_and_display_menu(menu_data)
            
            # 保存数据
            extractor.save_menu_data(menu_data)
            
            print(f"\n✅ 成功获取Main菜单信息")
        else:
            print("❌ 未能获取Main菜单信息")
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        extractor.disconnect()


if __name__ == "__main__":
    main()
