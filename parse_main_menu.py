#!/usr/bin/env python3
"""
解析Main菜单数据，清理ANSI转义序列并提取结构化信息
"""

import json
import re
from datetime import datetime

class MainMenuParser:
    """Main菜单解析器"""
    
    def __init__(self, json_file="main_menu_data.json"):
        self.json_file = json_file
        self.menu_data = None
        self.parsed_info = {}
    
    def load_menu_data(self):
        """加载菜单数据"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                self.menu_data = json.load(f)
            print(f"成功加载菜单数据: {self.json_file}")
            return True
        except Exception as e:
            print(f"加载菜单数据失败: {e}")
            return False
    
    def clean_ansi_sequences(self, text):
        """清理ANSI转义序列"""
        # 移除ANSI转义序列
        ansi_escape = re.compile(r'\x1b\[[0-9;]*[mHJK]|\x1b\[[0-9;]*[A-Za-z]')
        cleaned = ansi_escape.sub('', text)
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        return cleaned.strip()
    
    def extract_menu_items(self, content):
        """提取菜单项信息"""
        cleaned_content = self.clean_ansi_sequences(content)
        
        # 分析BIOS信息
        bios_info = {}
        menu_items = []
        
        # 提取基本BIOS信息
        patterns = {
            'bios_version': r'BIOS Version\s+([^\s]+)',
            'build_date': r'Build Date\s+([^\s]+)',
            'debug_mode': r'Debug Mode Status\s+([^\s]+)',
            'product_name': r'Product Name\s+([^\s]+(?:\s+[^\s]+)*)',
            'serial_number': r'Serial Number\s+([^\s]+)',
            'asset_tag': r'Asset Tag\s+([^\s]+)',
            'access_level': r'Access Level\s+([^\s]+)',
            'platform': r'Platform\s+([^\s]+)',
            'processor': r'Processor\s+([^\s]+(?:\s+[^\s]+)*)',
            'pch': r'PCH\s+([^\s]+(?:\s+[^\s]+)*)',
            'rc_revision': r'RC Revision\s+([^\s]+)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, cleaned_content, re.IGNORECASE)
            if match:
                bios_info[key] = match.group(1).strip()
        
        # 提取菜单标签
        menu_tabs = []
        tab_pattern = r'Main\s+Advanced\s+Platform Configuration\s+Socket Configuration\s+Server Mgmt'
        if re.search(tab_pattern, cleaned_content):
            menu_tabs = ['Main', 'Advanced', 'Platform Configuration', 'Socket Configuration', 'Server Mgmt']
        
        # 提取功能键说明
        function_keys = {}
        key_patterns = {
            'select_screen': r'><:\s*Select Screen',
            'select_item': r'\^v:\s*Select Item',
            'enter_select': r'Enter:\s*Select',
            'change_option': r'\+/-:\s*Change Option',
            'scroll_help': r'k/m:\s*Scroll Help Area',
            'general_help': r'F1:\s*General Help',
            'previous_values': r'F2:\s*Previous Values',
            'optimized_defaults': r'F3:\s*Optimized Defaults',
            'save_exit': r'F4:\s*Save & Exit',
            'search_items': r'F7:\s*Search Setup Items',
            'exit': r'ESC:\s*Exit'
        }
        
        for key, pattern in key_patterns.items():
            if re.search(pattern, cleaned_content, re.IGNORECASE):
                function_keys[key] = True
        
        return {
            'bios_info': bios_info,
            'menu_tabs': menu_tabs,
            'function_keys': function_keys,
            'cleaned_content': cleaned_content
        }
    
    def parse_menu_structure(self):
        """解析菜单结构"""
        if not self.menu_data or not self.menu_data.get('contents'):
            print("没有菜单内容可解析")
            return None
        
        main_content = self.menu_data['contents'][0]
        parsed = self.extract_menu_items(main_content)
        
        self.parsed_info = {
            'extraction_info': {
                'timestamp': self.menu_data.get('timestamp'),
                'host': self.menu_data.get('host'),
                'menu_type': self.menu_data.get('menu_type')
            },
            'bios_system_info': parsed['bios_info'],
            'menu_navigation': {
                'available_tabs': parsed['menu_tabs'],
                'current_tab': 'Main',
                'function_keys': parsed['function_keys']
            },
            'raw_content_cleaned': parsed['cleaned_content']
        }
        
        return self.parsed_info
    
    def display_parsed_info(self):
        """显示解析后的信息"""
        if not self.parsed_info:
            print("没有解析信息可显示")
            return
        
        print("\n" + "="*60)
        print("BIOS Main菜单解析结果")
        print("="*60)
        
        # 提取信息
        extraction_info = self.parsed_info.get('extraction_info', {})
        print(f"\n📊 提取信息:")
        print(f"  时间: {extraction_info.get('timestamp', 'N/A')}")
        print(f"  主机: {extraction_info.get('host', 'N/A')}")
        print(f"  菜单: {extraction_info.get('menu_type', 'N/A')}")
        
        # BIOS系统信息
        bios_info = self.parsed_info.get('bios_system_info', {})
        print(f"\n🖥️  BIOS系统信息:")
        info_items = [
            ('BIOS版本', 'bios_version'),
            ('构建日期', 'build_date'),
            ('调试模式', 'debug_mode'),
            ('产品名称', 'product_name'),
            ('序列号', 'serial_number'),
            ('资产标签', 'asset_tag'),
            ('访问级别', 'access_level'),
            ('平台', 'platform'),
            ('处理器', 'processor'),
            ('PCH', 'pch'),
            ('RC版本', 'rc_revision')
        ]
        
        for label, key in info_items:
            value = bios_info.get(key, 'N/A')
            print(f"  {label:12}: {value}")
        
        # 菜单导航信息
        nav_info = self.parsed_info.get('menu_navigation', {})
        print(f"\n🧭 菜单导航:")
        tabs = nav_info.get('available_tabs', [])
        if tabs:
            print(f"  可用标签: {' | '.join(tabs)}")
            print(f"  当前标签: {nav_info.get('current_tab', 'N/A')}")
        
        # 功能键
        func_keys = nav_info.get('function_keys', {})
        if func_keys:
            print(f"\n⌨️  可用功能键:")
            key_descriptions = {
                'select_screen': '><: 选择屏幕',
                'select_item': '^v: 选择项目',
                'enter_select': 'Enter: 确认选择',
                'change_option': '+/-: 更改选项',
                'scroll_help': 'k/m: 滚动帮助区域',
                'general_help': 'F1: 常规帮助',
                'previous_values': 'F2: 恢复之前值',
                'optimized_defaults': 'F3: 优化默认值',
                'save_exit': 'F4: 保存并退出',
                'search_items': 'F7: 搜索设置项',
                'exit': 'ESC: 退出'
            }
            
            for key, desc in key_descriptions.items():
                if func_keys.get(key):
                    print(f"    {desc}")
    
    def save_parsed_info(self, filename="main_menu_parsed.json"):
        """保存解析后的信息"""
        if not self.parsed_info:
            print("没有解析信息可保存")
            return False
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.parsed_info, f, indent=2, ensure_ascii=False)
            print(f"\n💾 解析结果已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存解析结果失败: {e}")
            return False
    
    def generate_summary(self):
        """生成摘要信息"""
        if not self.parsed_info:
            return None
        
        bios_info = self.parsed_info.get('bios_system_info', {})
        nav_info = self.parsed_info.get('menu_navigation', {})
        
        summary = {
            'server_model': bios_info.get('product_name', 'Unknown'),
            'bios_version': bios_info.get('bios_version', 'Unknown'),
            'serial_number': bios_info.get('serial_number', 'Unknown'),
            'processor_type': bios_info.get('processor', 'Unknown'),
            'available_menus': len(nav_info.get('available_tabs', [])),
            'access_level': bios_info.get('access_level', 'Unknown'),
            'extraction_time': self.parsed_info.get('extraction_info', {}).get('timestamp', 'Unknown')
        }
        
        return summary


def main():
    """主函数"""
    print("BIOS Main菜单数据解析器")
    print("="*40)
    
    parser = MainMenuParser()
    
    # 加载数据
    if not parser.load_menu_data():
        return
    
    # 解析菜单结构
    parsed_info = parser.parse_menu_structure()
    
    if parsed_info:
        # 显示解析结果
        parser.display_parsed_info()
        
        # 保存解析结果
        parser.save_parsed_info()
        
        # 生成摘要
        summary = parser.generate_summary()
        if summary:
            print(f"\n📋 系统摘要:")
            print(f"  服务器型号: {summary['server_model']}")
            print(f"  BIOS版本: {summary['bios_version']}")
            print(f"  处理器: {summary['processor_type']}")
            print(f"  可用菜单: {summary['available_menus']} 个")
            print(f"  访问级别: {summary['access_level']}")
        
        print(f"\n✅ Main菜单解析完成")
    else:
        print("❌ 菜单解析失败")


if __name__ == "__main__":
    main()
