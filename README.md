# BIOS菜单结构提取器

基于SOL (Serial Over LAN) 连接的BIOS菜单结构自动提取工具。通过模拟键盘操作来遍历BIOS界面，获取完整的菜单结构信息。

## 功能特性

- **自动SOL连接**: 通过IPMI协议建立到远程服务器BMC的SOL连接
- **智能菜单解析**: 自动识别BIOS菜单项、选中状态、值和子菜单
- **完整结构提取**: 遍历所有主菜单标签页和子菜单
- **数据持久化**: 将菜单结构保存为JSON格式
- **非阻塞I/O**: 高效的屏幕内容读取机制
- **错误处理**: 完善的异常处理和连接管理

## 文件结构

```
├── bios_menu_extractor.py  # 主要功能模块
├── test_bios_extractor.py  # 测试脚本
├── config.py               # 配置文件
├── sol.py                  # 原始参考实现
└── README.md              # 说明文档
```

## 依赖要求

- Python 3.7+
- ipmitool (系统命令)
- 标准库: subprocess, time, os, fcntl, select, re, json, typing, dataclasses

## 安装ipmitool

### Ubuntu/Debian
```bash
sudo apt-get install ipmitool
```

### CentOS/RHEL
```bash
sudo yum install ipmitool
```

### 验证安装
```bash
ipmitool -V
```

## 使用方法

### 1. 基本使用

```python
from bios_menu_extractor import BiosNavigator

# 创建导航器实例
navigator = BiosNavigator(
    host="************",
    username="Administrator", 
    password="Superuser9!"
)

# 提取完整菜单结构
menu_structure = navigator.extract_complete_menu_structure()

# 打印结构概览
navigator.print_menu_structure()

# 保存到JSON文件
navigator.save_menu_structure("my_bios_menu.json")
```

### 2. 运行主程序

```bash
python bios_menu_extractor.py
```

### 3. 运行测试

```bash
python test_bios_extractor.py
```

测试选项:
- `1` - 测试菜单解析功能
- `2` - 测试SOL连接
- `3` - 测试单次导航
- `4` - 完整菜单提取
- `q` - 退出

## 配置说明

在 `config.py` 中可以修改以下配置:

### SOL连接配置
```python
SOL_CONFIG = {
    'host': '************',      # BMC IP地址
    'username': 'Administrator',  # BMC用户名
    'password': 'Superuser9!',   # BMC密码
    'timeout': 10,               # 连接超时时间
    'retry_count': 3             # 重试次数
}
```

### 菜单解析配置
```python
MENU_PARSE_CONFIG = {
    'max_menu_depth': 3,         # 最大菜单深度
    'max_tabs': 10,              # 最大标签页数
    'navigation_delay': 0.5,     # 导航延迟时间
    'read_timeout': 5.0          # 读取超时时间
}
```

## 数据结构

### BiosMenuItem
```python
@dataclass
class BiosMenuItem:
    name: str                    # 菜单项名称
    position: Tuple[int, int]    # 位置 (行, 列)
    is_selected: bool            # 是否选中
    is_submenu: bool             # 是否为子菜单
    value: Optional[str]         # 当前值
    options: List[str]           # 可选项列表
```

### BiosMenu
```python
@dataclass 
class BiosMenu:
    title: str                   # 菜单标题
    items: List[BiosMenuItem]    # 菜单项列表
    current_selection: int       # 当前选中项索引
    raw_content: str             # 原始屏幕内容
```

### BiosMenuStructure
```python
@dataclass
class BiosMenuStructure:
    main_menus: List[BiosMenu]   # 主菜单列表
    current_menu_index: int      # 当前菜单索引
    navigation_path: List[str]   # 导航路径
```

## 输出示例

### 控制台输出
```
=== BIOS菜单结构概览 ===

主菜单 1: Main
  包含 6 个菜单项:
    1. System Information [当前选中]
    2. System Time (值: 14:30:25)
    3. System Date (值: 07/10/2025)
    4. BIOS Version (值: 1.23.45)
    5. Advanced Configuration [子菜单]
    6. Security Settings [子菜单]

主菜单 2: Advanced
  包含 4 个菜单项:
    1. CPU Configuration [子菜单]
    2. Memory Settings [子菜单]
    3. PCIe Configuration [子菜单]
    4. Power Management [子菜单]
```

### JSON输出
```json
{
  "main_menus": [
    {
      "title": "Main",
      "items": [
        {
          "name": "System Information",
          "position": [5, 6],
          "is_selected": true,
          "is_submenu": false,
          "value": null,
          "options": null
        }
      ],
      "current_selection": 0,
      "raw_content": "..."
    }
  ],
  "extraction_time": "2025-07-10 14:30:25",
  "host": "************"
}
```

## 注意事项

1. **权限要求**: 需要BMC管理员权限
2. **网络连接**: 确保能够访问目标服务器的BMC
3. **BIOS状态**: 目标服务器需要处于BIOS界面
4. **超时设置**: 根据网络延迟调整超时参数
5. **安全考虑**: 避免在生产环境中频繁操作

## 故障排除

### 连接失败
- 检查BMC IP地址、用户名和密码
- 确认ipmitool已正确安装
- 验证网络连通性

### 读取内容为空
- 增加读取超时时间
- 检查服务器是否在BIOS界面
- 尝试手动发送按键测试

### 菜单解析错误
- 检查BIOS界面格式
- 调整菜单识别模式
- 启用详细日志输出

## 扩展开发

可以通过以下方式扩展功能:

1. **添加新的按键操作**
2. **支持更多BIOS厂商格式**
3. **实现菜单项值的修改**
4. **添加批量操作功能**
5. **集成到自动化运维平台**

## 许可证

本项目仅供学习和研究使用。
