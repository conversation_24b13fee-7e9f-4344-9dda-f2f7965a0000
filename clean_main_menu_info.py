#!/usr/bin/env python3
"""
清理和整理Main菜单信息，提取出清晰的BIOS配置数据
"""

import json
import re
from datetime import datetime

def clean_main_menu_data():
    """清理Main菜单数据并提取关键信息"""
    
    # 从原始数据中手动提取清晰的信息
    # 基于我们看到的输出内容
    
    clean_info = {
        "extraction_info": {
            "timestamp": "2025-07-10T20:36:48.457791",
            "host": "************",
            "menu_type": "Main",
            "bios_interface": "Aptio Setup - AMI"
        },
        
        "system_information": {
            "bios_version": "***********",
            "build_date": "06/30/2025",
            "debug_mode_status": "Disabled",
            "product_name": "R5300 G5",
            "serial_number": "RcdTAu1nTm6tOOBMncCwhLM-bptXKey6eBMJrUjdaZt2Yen-guox1BrXeKKbZYpLl",
            "asset_tag": "MI05102A",
            "access_level": "Administrator"
        },
        
        "platform_information": {
            "platform": "TypeArcherCityRP",
            "processor": "C06F2 - EMR-SP Rx",
            "pch": "EBG A0/A1/B0/B1 SKU - B1",
            "rc_revision": "114.D30"
        },
        
        "menu_structure": {
            "available_tabs": [
                "Main",
                "Advanced", 
                "Platform Configuration",
                "Socket Configuration",
                "Server Mgmt"
            ],
            "current_tab": "Main",
            "total_tabs": 5
        },
        
        "navigation_controls": {
            "screen_navigation": {
                "left_right_arrows": "Select Screen (><)",
                "up_down_arrows": "Select Item (^v)",
                "enter": "Select/Confirm",
                "plus_minus": "Change Option (+/-)"
            },
            "function_keys": {
                "F1": "General Help",
                "F2": "Previous Values", 
                "F3": "Optimized Defaults",
                "F4": "Save & Exit",
                "F7": "Search Setup Items",
                "ESC": "Exit"
            },
            "help_navigation": {
                "k_m": "Scroll Help Area"
            }
        },
        
        "main_menu_items": [
            {
                "category": "System Information",
                "items": [
                    {"name": "BIOS Version", "value": "***********", "type": "readonly"},
                    {"name": "Build Date", "value": "06/30/2025", "type": "readonly"},
                    {"name": "Debug Mode Status", "value": "Disabled", "type": "readonly"},
                    {"name": "Product Name", "value": "R5300 G5", "type": "readonly"},
                    {"name": "Serial Number", "value": "RcdTAu1nTm6tOOBMncCwhLM-bptXKey6eBMJrUjdaZt2Yen-guox1BrXeKKbZYpLl", "type": "readonly"},
                    {"name": "Asset Tag", "value": "MI05102A", "type": "configurable"}
                ]
            },
            {
                "category": "Platform Information", 
                "items": [
                    {"name": "Platform", "value": "TypeArcherCityRP", "type": "readonly"},
                    {"name": "Processor", "value": "C06F2 - EMR-SP Rx", "type": "readonly"},
                    {"name": "PCH", "value": "EBG A0/A1/B0/B1 SKU - B1", "type": "readonly"},
                    {"name": "RC Revision", "value": "114.D30", "type": "readonly"}
                ]
            }
        ],
        
        "bios_metadata": {
            "manufacturer": "AMI",
            "interface_version": "Version 2.22.1290",
            "copyright": "Copyright (C) 2025 AMI",
            "setup_utility": "Aptio Setup"
        }
    }
    
    return clean_info

def display_clean_info(info):
    """显示清理后的信息"""
    print("\n" + "="*70)
    print("BIOS Main菜单 - 清理后的信息")
    print("="*70)
    
    # 系统信息
    sys_info = info['system_information']
    print(f"\n🖥️  系统信息:")
    print(f"  产品型号: {sys_info['product_name']}")
    print(f"  序列号: {sys_info['serial_number']}")
    print(f"  资产标签: {sys_info['asset_tag']}")
    print(f"  访问级别: {sys_info['access_level']}")
    
    # BIOS信息
    print(f"\n💾 BIOS信息:")
    print(f"  BIOS版本: {sys_info['bios_version']}")
    print(f"  构建日期: {sys_info['build_date']}")
    print(f"  调试模式: {sys_info['debug_mode_status']}")
    print(f"  制造商: {info['bios_metadata']['manufacturer']}")
    print(f"  接口版本: {info['bios_metadata']['interface_version']}")
    
    # 平台信息
    platform_info = info['platform_information']
    print(f"\n🔧 平台信息:")
    print(f"  平台类型: {platform_info['platform']}")
    print(f"  处理器: {platform_info['processor']}")
    print(f"  PCH芯片: {platform_info['pch']}")
    print(f"  RC版本: {platform_info['rc_revision']}")
    
    # 菜单结构
    menu_info = info['menu_structure']
    print(f"\n📋 菜单结构:")
    print(f"  当前标签: {menu_info['current_tab']}")
    print(f"  可用标签: {', '.join(menu_info['available_tabs'])}")
    print(f"  总标签数: {menu_info['total_tabs']}")
    
    # 导航控制
    nav_info = info['navigation_controls']
    print(f"\n⌨️  导航控制:")
    print(f"  屏幕导航: {nav_info['screen_navigation']['left_right_arrows']}")
    print(f"  项目选择: {nav_info['screen_navigation']['up_down_arrows']}")
    print(f"  确认选择: {nav_info['screen_navigation']['enter']}")
    print(f"  更改选项: {nav_info['screen_navigation']['plus_minus']}")
    
    print(f"\n  功能键:")
    for key, desc in nav_info['function_keys'].items():
        print(f"    {key}: {desc}")
    
    # 菜单项详情
    print(f"\n📝 Main菜单项详情:")
    for category in info['main_menu_items']:
        print(f"\n  {category['category']}:")
        for item in category['items']:
            type_indicator = " [可配置]" if item['type'] == 'configurable' else " [只读]"
            print(f"    • {item['name']}: {item['value']}{type_indicator}")

def save_clean_info(info, filename="main_menu_clean.json"):
    """保存清理后的信息"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        print(f"\n💾 清理后的信息已保存到: {filename}")
        return True
    except Exception as e:
        print(f"保存失败: {e}")
        return False

def generate_summary_report(info):
    """生成摘要报告"""
    sys_info = info['system_information']
    platform_info = info['platform_information']
    
    summary = f"""
BIOS Main菜单摘要报告
{'='*50}
提取时间: {info['extraction_info']['timestamp']}
目标主机: {info['extraction_info']['host']}

硬件信息:
  服务器型号: {sys_info['product_name']}
  处理器: {platform_info['processor']}
  平台: {platform_info['platform']}
  PCH: {platform_info['pch']}

BIOS信息:
  版本: {sys_info['bios_version']}
  构建日期: {sys_info['build_date']}
  制造商: {info['bios_metadata']['manufacturer']}
  
系统标识:
  序列号: {sys_info['serial_number']}
  资产标签: {sys_info['asset_tag']}
  访问级别: {sys_info['access_level']}

菜单导航:
  可用菜单: {len(info['menu_structure']['available_tabs'])} 个
  当前位置: {info['menu_structure']['current_tab']}
  
配置项统计:
  只读项目: {sum(1 for cat in info['main_menu_items'] for item in cat['items'] if item['type'] == 'readonly')} 个
  可配置项: {sum(1 for cat in info['main_menu_items'] for item in cat['items'] if item['type'] == 'configurable')} 个
"""
    
    return summary

def main():
    """主函数"""
    print("BIOS Main菜单信息清理工具")
    print("="*40)
    
    # 清理数据
    clean_info = clean_main_menu_data()
    
    # 显示清理后的信息
    display_clean_info(clean_info)
    
    # 保存清理后的数据
    save_clean_info(clean_info)
    
    # 生成摘要报告
    summary = generate_summary_report(clean_info)
    print(summary)
    
    # 保存摘要报告
    with open("main_menu_summary.txt", 'w', encoding='utf-8') as f:
        f.write(summary)
    print("📄 摘要报告已保存到: main_menu_summary.txt")
    
    print(f"\n✅ Main菜单信息清理完成")

if __name__ == "__main__":
    main()
