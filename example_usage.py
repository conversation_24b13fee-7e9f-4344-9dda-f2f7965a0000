#!/usr/bin/env python3
"""
BIOS菜单提取器使用示例
演示如何使用bios_menu_extractor模块
"""

from bios_menu_extractor import BiosNavigator, BiosMenuStructure
from config import SOL_CONFIG
import json

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 使用配置文件中的参数
    navigator = BiosNavigator(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    # 提取菜单结构
    print("开始提取BIOS菜单结构...")
    menu_structure = navigator.extract_complete_menu_structure()
    
    if menu_structure.main_menus:
        print(f"成功提取了 {len(menu_structure.main_menus)} 个主菜单")
        
        # 显示结构概览
        navigator.print_menu_structure()
        
        # 保存结果
        navigator.save_menu_structure("example_bios_menu.json")
        
        return menu_structure
    else:
        print("未能提取到菜单结构")
        return None

def example_custom_analysis(menu_structure: BiosMenuStructure):
    """自定义分析示例"""
    if not menu_structure or not menu_structure.main_menus:
        print("没有可分析的菜单结构")
        return
    
    print("\n=== 自定义分析示例 ===")
    
    # 统计信息
    total_items = sum(len(menu.items) for menu in menu_structure.main_menus)
    submenu_count = 0
    value_items = 0
    
    for menu in menu_structure.main_menus:
        for item in menu.items:
            if item.is_submenu:
                submenu_count += 1
            if item.value:
                value_items += 1
    
    print(f"统计信息:")
    print(f"  总菜单数: {len(menu_structure.main_menus)}")
    print(f"  总菜单项: {total_items}")
    print(f"  子菜单数: {submenu_count}")
    print(f"  有值项目: {value_items}")
    
    # 查找特定菜单项
    print(f"\n查找包含'Security'的菜单项:")
    for menu in menu_structure.main_menus:
        for item in menu.items:
            if 'security' in item.name.lower():
                print(f"  在 '{menu.title}' 中找到: {item.name}")
    
    # 查找所有配置值
    print(f"\n当前配置值:")
    for menu in menu_structure.main_menus:
        for item in menu.items:
            if item.value and not item.is_submenu:
                print(f"  {item.name}: {item.value}")

def example_connection_test():
    """连接测试示例"""
    print("=== 连接测试示例 ===")
    
    navigator = BiosNavigator(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    # 仅测试连接
    if navigator.connect_sol():
        print("SOL连接成功")
        
        # 读取当前屏幕
        content = navigator.read_screen_content(3.0)
        if content:
            print(f"读取到屏幕内容 ({len(content)} 字符)")
            
            # 解析当前屏幕
            menu = navigator.parse_bios_menu(content)
            print(f"当前菜单: {menu.title}")
            print(f"菜单项数量: {len(menu.items)}")
            
            if menu.items:
                print("前5个菜单项:")
                for i, item in enumerate(menu.items[:5]):
                    status = " [选中]" if item.is_selected else ""
                    value = f" = {item.value}" if item.value else ""
                    print(f"  {i+1}. {item.name}{value}{status}")
        
        navigator.disconnect()
    else:
        print("SOL连接失败")

def example_step_by_step():
    """分步骤操作示例"""
    print("=== 分步骤操作示例 ===")
    
    navigator = BiosNavigator(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    if not navigator.connect_sol():
        print("连接失败")
        return
    
    try:
        # 步骤1: 读取初始状态
        print("步骤1: 读取初始BIOS屏幕...")
        initial_content = navigator.read_screen_content(3.0)
        if initial_content:
            initial_menu = navigator.parse_bios_menu(initial_content)
            print(f"  当前菜单: {initial_menu.title}")
        
        # 步骤2: 导航到主菜单
        print("步骤2: 确保在主菜单...")
        navigator.navigate_to_main_menu()
        
        # 步骤3: 扫描主菜单标签
        print("步骤3: 扫描主菜单标签...")
        main_menus = navigator.scan_main_menu_tabs()
        print(f"  发现 {len(main_menus)} 个主菜单标签")
        
        for i, menu in enumerate(main_menus):
            print(f"    {i+1}. {menu.title} ({len(menu.items)} 项)")
        
        # 步骤4: 保存结果
        if main_menus:
            navigator.menu_structure.main_menus = main_menus
            navigator.save_menu_structure("step_by_step_result.json")
            print("步骤4: 结果已保存")
        
    except Exception as e:
        print(f"操作过程中出错: {e}")
    finally:
        navigator.disconnect()

def main():
    """主函数"""
    print("BIOS菜单提取器使用示例")
    print("=" * 50)
    
    examples = {
        '1': ('基本使用示例', example_basic_usage),
        '2': ('连接测试示例', example_connection_test),
        '3': ('分步骤操作示例', example_step_by_step),
    }
    
    print("可用示例:")
    for key, (name, _) in examples.items():
        print(f"  {key} - {name}")
    print("  q - 退出")
    
    while True:
        choice = input("\n请选择示例 (1-3, q): ").strip()
        
        if choice.lower() == 'q':
            break
        elif choice in examples:
            name, func = examples[choice]
            print(f"\n执行: {name}")
            print("-" * 30)
            
            try:
                result = func()
                
                # 如果是基本使用示例，继续进行自定义分析
                if choice == '1' and result:
                    example_custom_analysis(result)
                    
            except KeyboardInterrupt:
                print("\n操作被用户中断")
            except Exception as e:
                print(f"示例执行出错: {e}")
        else:
            print("无效选择，请重试")
    
    print("示例演示结束")

if __name__ == "__main__":
    main()
