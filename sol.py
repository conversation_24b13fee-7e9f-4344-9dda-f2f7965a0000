import subprocess
import time
import os
import fcntl
import select
import re
import json
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict

@dataclass
class BiosMenuItem:
    """BIOS菜单项数据结构"""
    name: str
    position: Tuple[int, int]  # (row, col)
    is_selected: bool = False
    is_submenu: bool = False
    value: Optional[str] = None
    options: List[str] = None

@dataclass
class BiosMenu:
    """BIOS菜单页面数据结构"""
    title: str
    items: List[BiosMenuItem]
    current_selection: int = 0
    raw_content: str = ""

@dataclass
class BiosMenuStructure:
    """完整的BIOS菜单结构"""
    main_menus: List[BiosMenu]
    current_menu_index: int = 0
    navigation_path: List[str] = None

class BiosNavigator:
    """BIOS导航和菜单结构提取器"""

    # ANSI转义序列定义
    KEY_UP = '\x1b[A'
    KEY_DOWN = '\x1b[B'
    KEY_RIGHT = '\x1b[C'
    KEY_LEFT = '\x1b[D'
    KEY_ENTER = '\r'
    KEY_ESC = '\x1b'
    KEY_F10 = '\x1b[21~'  # 通常用于保存并退出

    def __init__(self, host: str, username: str, password: str):
        self.host = host
        self.username = username
        self.password = password
        self.sol_process = None
        self.menu_structure = BiosMenuStructure(main_menus=[], navigation_path=[])

    def connect_sol(self) -> bool:
        """建立SOL连接"""
        cmd = ['ipmitool', '-I', 'lanplus', '-H', self.host,
               '-U', self.username, '-P', self.password,
               'sol', 'activate']

        print(f"连接到 {self.host}...")
        try:
            self.sol_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            time.sleep(3)  # 等待连接建立

            if self.sol_process.poll() is not None:
                stdout, stderr = self.sol_process.communicate()
                print(f"SOL连接失败: {stderr}")
                return False

            # 设置非阻塞I/O
            self._setup_nonblocking_io()
            print("SOL连接成功")
            return True

        except Exception as e:
            print(f"SOL连接异常: {e}")
            return False

    def _setup_nonblocking_io(self):
        """设置非阻塞I/O"""
        if self.sol_process:
            fd_out = self.sol_process.stdout.fileno()
            fd_err = self.sol_process.stderr.fileno()

            fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
            fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)

            fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
            fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)

    def send_key(self, key: str, wait_time: float = 0.5) -> bool:
        """发送按键"""
        if not self.sol_process or self.sol_process.poll() is not None:
            print("SOL连接已断开")
            return False

        try:
            self.sol_process.stdin.write(key)
            self.sol_process.stdin.flush()
            time.sleep(wait_time)
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False

    def read_screen_content(self, timeout: float = 5.0) -> str:
        """读取屏幕内容"""
        if not self.sol_process:
            return ""

        outputs = []
        start_time = time.time()
        no_data_count = 0
        max_no_data = int(timeout * 10)  # 0.1秒间隔

        while time.time() - start_time < timeout:
            if self.sol_process.poll() is not None:
                break

            ready, _, _ = select.select([self.sol_process.stdout], [], [], 0.1)
            if not ready:
                no_data_count += 1
                if no_data_count > max_no_data:
                    break
                continue

            no_data_count = 0
            try:
                fd = self.sol_process.stdout.fileno()
                data = os.read(fd, 4096)
                if data:
                    outputs.append(data.decode('utf-8', errors='ignore'))
            except Exception as e:
                print(f"读取屏幕内容出错: {e}")
                break

        return ''.join(outputs)

    def parse_bios_menu(self, content: str) -> BiosMenu:
        """解析BIOS菜单内容"""
        lines = content.split('\n')
        menu_items = []
        title = ""
        current_selection = 0

        # 查找菜单标题（通常在顶部）
        for i, line in enumerate(lines[:10]):
            line = line.strip()
            if line and not line.startswith('[') and len(line) > 5:
                if any(keyword in line.lower() for keyword in ['setup', 'bios', 'configuration', 'menu']):
                    title = line
                    break

        # 解析菜单项
        for i, line in enumerate(lines):
            line_clean = line.strip()
            if not line_clean:
                continue

            # 检测菜单项模式
            # 通常BIOS菜单项格式: "  > Item Name" 或 "    Item Name" 或 "[*] Item Name"
            is_selected = False
            is_submenu = False
            item_name = ""
            value = None

            # 检测选中状态
            if '>' in line or line_clean.startswith('[') or line_clean.startswith('*'):
                is_selected = True
                current_selection = len(menu_items)

            # 提取菜单项名称
            # 移除选择标记和前导空格
            clean_line = re.sub(r'^[\s\>\[\]\*\-\+]*', '', line_clean)

            # 检测是否有值（如 "Boot Order    : UEFI"）
            if ':' in clean_line:
                parts = clean_line.split(':', 1)
                item_name = parts[0].strip()
                value = parts[1].strip() if len(parts) > 1 else None
            else:
                item_name = clean_line

            # 检测子菜单（通常包含特定关键词或符号）
            if any(keyword in item_name.lower() for keyword in ['setup', 'configuration', 'advanced', 'security']):
                is_submenu = True

            # 过滤有效的菜单项
            if item_name and len(item_name) > 1 and not item_name.isspace():
                # 估算位置（简化处理）
                position = (i, line.find(item_name) if item_name in line else 0)

                menu_item = BiosMenuItem(
                    name=item_name,
                    position=position,
                    is_selected=is_selected,
                    is_submenu=is_submenu,
                    value=value
                )
                menu_items.append(menu_item)

        return BiosMenu(
            title=title or "BIOS Menu",
            items=menu_items,
            current_selection=current_selection,
            raw_content=content
        )

def get_bios_info():

    print("=== 测试SOL连接和输出读取 ===")

    outputs = []
    stderr_outputs = []

    cmd = ['ipmitool', '-I', 'lanplus', '-H', '************',
           '-U', 'Administrator', '-P', 'Superuser9!',
           'sol', 'activate'
            ]

    print("启动SOL进程...")
    sol_process = subprocess.Popen(
        cmd,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1,
        universal_newlines=True
    )

    print("等待进程启动...")
    time.sleep(3)
    
    # 检查进程状态
    if sol_process.poll() is not None:
        stdout, stderr = sol_process.communicate()
        print(f"进程已退出，返回码: {sol_process.returncode}")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
    else:
        print("进程正在运行，尝试读取输出...")
        
        start_time = time.time()
        timeout = 10  # 10秒超时
        no_data_count = 0  # 连续无数据计数
        max_no_data = 50   # 最多等待5秒

        print("读取初始输出...")
        time.sleep(1)
        # print("发送上箭头按键...")
        # sol_process.stdin.write('\x1b[A')
        # print("发送下箭头按键...")
        # sol_process.stdin.write('\x1b[B')
        print("发送右箭头按键...")
        sol_process.stdin.write('\x1b[C')
        sol_process.stdin.flush()
        time.sleep(0.5)  # 给BMC反应时间
        # print("发送左箭头按键...")
        # sol_process.stdin.write('\x1b[D')
        # sol_process.stdin.flush()
        # time.sleep(0.5)  # 给BMC反应时间

        fd_out = sol_process.stdout.fileno()
        fd_err = sol_process.stderr.fileno()
        # 设置非阻塞
        fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
        fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)
        fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
        fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)

        while time.time() - start_time < timeout:
            if sol_process.poll() is not None:
                print("进程已退出")
                break

            ready, _, _ = select.select([sol_process.stdout, sol_process.stderr], [], [], 0.1)
            if not ready:
                no_data_count += 1
                if no_data_count > max_no_data:
                    break
                continue

            no_data_count = 0
            for stream in ready:
                try:
                    fd = stream.fileno()
                    data = os.read(fd, 1024)
                    if data:
                        if stream == sol_process.stdout:
                            outputs.append(data)
                        elif stream == sol_process.stderr:
                            stderr_outputs.append(data)
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    print(f"读取流时出错: {e}")
                    break

    # if outputs:
    #     print("STDOUT输出内容:")
    #     for i, output in enumerate(outputs):
    #         print(f"  {i+1}: {output}")
    
    # if stderr_outputs:
    #     print("STDERR输出内容:")
    #     for i, output in enumerate(stderr_outputs):
    #         print(f"  {i+1}: {output}")
    return outputs
