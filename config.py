#!/usr/bin/env python3
"""
BIOS菜单提取器配置文件
"""

# SOL连接配置
SOL_CONFIG = {
    'host': '************',
    'username': 'Administrator',
    'password': 'Superuser9!',
    'timeout': 10,
    'retry_count': 3
}

# 菜单解析配置
MENU_PARSE_CONFIG = {
    'max_menu_depth': 3,
    'max_tabs': 10,
    'navigation_delay': 0.5,
    'read_timeout': 5.0,
    'menu_keywords': ['setup', 'bios', 'configuration', 'menu'],
    'submenu_keywords': ['setup', 'configuration', 'advanced', 'security', 'boot', 'exit']
}

# 输出配置
OUTPUT_CONFIG = {
    'json_filename': 'bios_menu_structure.json',
    'log_filename': 'bios_extraction.log',
    'save_raw_content': True,
    'verbose': True
}

# ANSI按键映射
KEY_MAPPINGS = {
    'UP': '\x1b[A',
    'DOWN': '\x1b[B',
    'RIGHT': '\x1b[C', 
    'LEFT': '\x1b[D',
    'ENTER': '\r',
    'ESC': '\x1b',
    'F10': '\x1b[21~',
    'TAB': '\t',
    'SPACE': ' '
}

# 菜单项识别模式
MENU_PATTERNS = {
    'selected_indicators': ['>', '*', '[*]', '[x]'],
    'value_separator': ':',
    'submenu_indicators': ['>', '→', '»'],
    'option_brackets': ['[', ']', '(', ')']
}
